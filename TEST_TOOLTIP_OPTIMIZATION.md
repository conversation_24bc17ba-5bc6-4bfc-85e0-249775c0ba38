# ToolTipComponent 优化测试指南

## 修复内容总结

### 🔧 修复的问题
- **原问题**: 使用了不存在的 `editor.scrollPane` 属性
- **解决方案**: 改用 `editor.scrollingModel.visibleArea` 获取可视区域

### 📝 具体修改

#### 1. ToolTipComponent.kt
```kotlin
// 修改前（错误）:
val scrollPane = editor.scrollPane
val visibleRect = scrollPane.viewport.viewRect

// 修改后（正确）:
val visibleArea = editor.scrollingModel.visibleArea
```

#### 2. ContinuePluginSelectionListener.kt
```kotlin
// 修改前（错误）:
val visibleRect = editor.scrollPane.viewport.viewRect
val minY = visibleRect.y + editor.lineHeight

// 修改后（正确）:
val visibleArea = editor.scrollingModel.visibleArea
val minY = visibleArea.y + editor.lineHeight
```

## 🧪 测试场景

### 1. 基础功能测试
- [ ] 在编辑器中选择一行代码，验证tooltip正常显示
- [ ] 在编辑器中选择多行代码，验证tooltip正常显示
- [ ] 点击tooltip按钮，验证功能正常工作

### 2. 边界测试
- [ ] **右边界测试**: 在编辑器右边缘选择代码，tooltip应自动移到左侧
- [ ] **左边界测试**: 在编辑器左边缘选择代码，tooltip应保持在可视范围内
- [ ] **上边界测试**: 在编辑器顶部选择代码，tooltip应自动调整到下方
- [ ] **下边界测试**: 在编辑器底部选择代码，tooltip应自动调整到上方

### 3. 滚动测试
- [ ] 滚动编辑器到不同位置后选择代码，tooltip应始终在可视范围内
- [ ] 水平滚动后选择代码，tooltip位置应正确调整
- [ ] 垂直滚动后选择代码，tooltip位置应正确调整

### 4. 窗口大小测试
- [ ] 调整IDE窗口大小，tooltip应适应新的可视区域
- [ ] 最小化/最大化窗口，tooltip应正确响应

### 5. 多行选择测试
- [ ] 选择跨多行的代码，tooltip应定位在合适位置
- [ ] 选择包含空行的多行代码，tooltip应正确处理

## 🔍 验证要点

### 1. 位置计算正确性
```kotlin
// 验证可视区域获取
val visibleArea = editor.scrollingModel.visibleArea
println("可视区域: x=${visibleArea.x}, y=${visibleArea.y}, width=${visibleArea.width}, height=${visibleArea.height}")

// 验证边界检测
val margin = 10
val adjustedX = if (preferredX + tooltipWidth > visibleArea.x + visibleArea.width - margin) {
    visibleArea.x + visibleArea.width - tooltipWidth - margin
} else {
    preferredX
}
```

### 2. 边距控制
- tooltip与编辑器边缘应保持10像素边距
- tooltip不应贴边显示
- tooltip不应超出可视区域

### 3. 性能验证
- tooltip位置计算应快速响应
- 不应影响编辑器的正常滚动性能
- 内存使用应保持稳定

## 🚀 预期效果

### ✅ 成功标准
1. **无需滚动**: 用户无需手动滚动即可看到tooltip
2. **智能定位**: tooltip自动选择最佳显示位置
3. **边界适应**: tooltip在任何位置都能正确显示
4. **稳定性**: 不会出现编译错误或运行时异常

### ❌ 失败标准
1. tooltip超出可视区域
2. tooltip位置计算错误
3. 出现编译错误
4. 影响编辑器正常功能

## 🔧 调试技巧

### 1. 添加调试日志
```kotlin
private fun calculateOptimalPosition(...): Pair<Int, Int> {
    val visibleArea = editor.scrollingModel.visibleArea
    println("DEBUG: visibleArea = $visibleArea")
    println("DEBUG: preferredX = $preferredX, preferredY = $preferredY")
    
    // ... 位置计算逻辑
    
    println("DEBUG: adjustedX = $adjustedX, adjustedY = $adjustedY")
    return Pair(adjustedX, adjustedY)
}
```

### 2. 可视化边界
可以临时添加边界线来可视化tooltip的定位逻辑：
```kotlin
// 临时添加边界矩形（仅用于调试）
val g2 = graphics as Graphics2D
g2.color = Color.RED
g2.drawRect(visibleArea.x, visibleArea.y, visibleArea.width, visibleArea.height)
```

## 📋 测试清单

- [ ] 代码编译通过
- [ ] 基础功能正常
- [ ] 边界检测工作
- [ ] 滚动适应正确
- [ ] 性能无影响
- [ ] 无内存泄漏
- [ ] 用户体验改善

## 🎯 下一步优化建议

1. **动画效果**: 可以添加tooltip位置调整的平滑动画
2. **智能避让**: 进一步优化tooltip与其他UI元素的避让逻辑
3. **自适应大小**: 根据内容动态调整tooltip大小
4. **主题适配**: 确保tooltip在不同主题下都有良好的显示效果
