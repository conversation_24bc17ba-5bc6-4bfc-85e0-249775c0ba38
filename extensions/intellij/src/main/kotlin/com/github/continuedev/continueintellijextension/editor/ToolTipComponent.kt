import com.github.continuedev.continueintellijextension.actions.focusContinueInput
import com.github.continuedev.continueintellijextension.editor.openInlineEdit
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.colors.EditorColorsManager
import com.intellij.openapi.editor.colors.EditorFontType
import com.intellij.openapi.util.IconLoader
import com.intellij.ui.components.JBPanel
import java.awt.*
import java.awt.event.ActionEvent
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import java.util.*
import javax.swing.JButton
import java.awt.geom.RoundRectangle2D

class StyledButton(text: String) : JButton(text) {
    private var isHovered = false
    private val editorBackground: Color

    init {
        border = null
        isContentAreaFilled = false
        isFocusPainted = false
        cursor = Cursor(Cursor.HAND_CURSOR)

        val scheme = EditorColorsManager.getInstance().globalScheme
        val editorFont = scheme.getFont(EditorFontType.PLAIN)
        val editorFontSize = editorFont.size

        font = font.deriveFont(editorFontSize.toFloat() * 0.75f)

        editorBackground = scheme.defaultBackground

        addMouseListener(object : MouseAdapter() {
            override fun mouseEntered(e: MouseEvent) {
                isHovered = true
                repaint()
            }

            override fun mouseExited(e: MouseEvent) {
                isHovered = false
                repaint()
            }
        })
    }


    override fun paintComponent(g: Graphics) {
        val g2 = g.create() as Graphics2D
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)

        val width = width.toFloat()
        val height = height.toFloat()
        val arc = 6f

        // Draw semi-transparent background
        g2.color = editorBackground
        g2.fill(RoundRectangle2D.Float(0f, 0f, width, height, arc, arc))

        // Draw border
        g2.color = if (isHovered) foreground else foreground.darker()
        g2.stroke = BasicStroke(1f)
        g2.draw(RoundRectangle2D.Float(0.5f, 0.5f, width - 1f, height - 1f, arc, arc))

        super.paintComponent(g)
        g2.dispose()
    }
}


class ToolTipComponent(editor: Editor, x: Int, y: Int) :
    JBPanel<ToolTipComponent>() {
    private var addToChatButton: StyledButton
    private var editButton: StyledButton

    init {
        layout = null // Remove the FlowLayout

        // Make the background transparent
        isOpaque = false
        background = Color(0, 0, 0, 0)

        val cmdCtrlChar =
            if (System.getProperty("os.name").lowercase(Locale.getDefault()).contains("mac")) "⌘" else "Ctrl"

        val buttonHeight = 16
        val buttonHorizontalPadding = 2
        val buttonVerticalPadding = 2
        val componentHorizontalPadding = 4
        val buttonMargin = 4

        addToChatButton = StyledButton("对话 (${cmdCtrlChar}+J)")
        editButton = StyledButton("编辑 (${cmdCtrlChar}+I)")
        addToChatButton.icon = IconLoader.getIcon("/icons/continue.svg", javaClass)
        editButton.icon = IconLoader.getIcon("/icons/continue.svg", javaClass)

        addToChatButton.addActionListener { e: ActionEvent? ->
            focusContinueInput(editor.project)
            editor.contentComponent.remove(this)
        }
        editButton.addActionListener { e: ActionEvent? ->
            openInlineEdit(editor.project, editor)
            editor.contentComponent.remove(this)
        }


        // Calculate button widths
        val addToChatWidth = addToChatButton.preferredSize.width + (2 * buttonHorizontalPadding)
        val editWidth = editButton.preferredSize.width + (2 * buttonHorizontalPadding)

        // Set bounds for buttons
        addToChatButton.setBounds(componentHorizontalPadding, buttonVerticalPadding, addToChatWidth, buttonHeight)
        editButton.setBounds(
            componentHorizontalPadding + addToChatWidth + buttonMargin,
            buttonVerticalPadding,
            editWidth,
            buttonHeight
        )

        add(addToChatButton)
        add(editButton)

        val totalWidth = addToChatWidth + editWidth + buttonMargin + (2 * componentHorizontalPadding)
        val totalHeight = buttonHeight + (2 * buttonVerticalPadding)

        // Calculate optimal position within editor's visible area
        val (adjustedX, adjustedY) = calculateOptimalPosition(editor, x, y, totalWidth, totalHeight)
        setBounds(adjustedX, adjustedY, totalWidth, totalHeight)
    }

    /**
     * 计算最优的tooltip位置，确保它始终在编辑器的可视范围内
     */
    private fun calculateOptimalPosition(
        editor: Editor,
        preferredX: Int,
        preferredY: Int,
        tooltipWidth: Int,
        tooltipHeight: Int
    ): Pair<Int, Int> {
        // 使用editor.scrollingModel.visibleArea获取可视区域
        val visibleArea = editor.scrollingModel.visibleArea

        // 计算可视区域的边界
        val visibleLeft = visibleArea.x
        val visibleTop = visibleArea.y
        val visibleRight = visibleArea.x + visibleArea.width
        val visibleBottom = visibleArea.y + visibleArea.height

        // 添加一些边距以避免tooltip贴边
        val margin = 10

        // 计算调整后的X坐标
        var adjustedX = preferredX
        if (adjustedX + tooltipWidth > visibleRight - margin) {
            // 如果tooltip会超出右边界，将其移到左边
            adjustedX = visibleRight - tooltipWidth - margin
        }
        if (adjustedX < visibleLeft + margin) {
            // 如果tooltip会超出左边界，将其移到右边
            adjustedX = visibleLeft + margin
        }

        // 计算调整后的Y坐标
        var adjustedY = preferredY - (tooltipHeight / 2) // 垂直居中
        if (adjustedY + tooltipHeight > visibleBottom - margin) {
            // 如果tooltip会超出底部边界，将其移到上方
            adjustedY = visibleBottom - tooltipHeight - margin
        }
        if (adjustedY < visibleTop + margin) {
            // 如果tooltip会超出顶部边界，将其移到下方
            adjustedY = visibleTop + margin
        }

        return Pair(adjustedX, adjustedY)
    }
}
